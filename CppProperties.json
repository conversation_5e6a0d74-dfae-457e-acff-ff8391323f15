{"configurations": [{"inheritEnvironments": ["msvc_x64"], "name": "x64-Debug", "includePath": ["${env.INCLUDE}", "${workspaceRoot}\\**", "C:\\Program Files\\LLVM\\include", "C:\\Program Files (x86)\\Windows Kits\\10\\Include\\10.0.19041.0\\ucrt", "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.38.33130\\include", "C:\\Program Files\\LLVM\\lib\\clang\\19.1.1\\include"], "defines": ["WIN32", "_DEBUG", "UNICODE", "_UNICODE"], "intelliSenseMode": "windows-clang-x64", "compilerPath": "C:\\Program Files\\LLVM\\bin\\clang++.exe", "cStandard": "c17", "cppStandard": "c++20"}]}